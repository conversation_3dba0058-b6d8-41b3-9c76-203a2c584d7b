{"files.associations": {"iosfwd": "cpp", "sstream": "cpp", "algorithm": "cpp", "memory": "cpp", "numeric": "cpp", "array": "cpp", "atomic": "cpp", "*.tcc": "cpp", "bitset": "cpp", "cctype": "cpp", "cfenv": "cpp", "chrono": "cpp", "clocale": "cpp", "cmath": "cpp", "condition_variable": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "list": "cpp", "unordered_map": "cpp", "vector": "cpp", "exception": "cpp", "functional": "cpp", "iterator": "cpp", "map": "cpp", "memory_resource": "cpp", "optional": "cpp", "ratio": "cpp", "set": "cpp", "string": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "ostream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "thread": "cpp", "typeinfo": "cpp", "*.ipp": "cpp", "strstream": "cpp", "codecvt": "cpp", "complex": "cpp", "csignal": "cpp", "forward_list": "cpp", "unordered_set": "cpp", "random": "cpp", "hash_map": "cpp", "slist": "cpp", "future": "cpp", "cinttypes": "cpp", "typeindex": "cpp", "valarray": "cpp", "filesystem": "cpp", "variant": "cpp", "regex": "cpp", "rope": "cpp", "any": "cpp", "source_location": "cpp", "shared_mutex": "cpp", "bit": "cpp", "charconv": "cpp", "compare": "cpp", "concepts": "cpp", "format": "cpp", "ios": "cpp", "locale": "cpp", "queue": "cpp", "stack": "cpp", "stop_token": "cpp", "xfacet": "cpp", "xhash": "cpp", "xiosbase": "cpp", "xlocale": "cpp", "xlocbuf": "cpp", "xlocinfo": "cpp", "xlocmes": "cpp", "xlocmon": "cpp", "xlocnum": "cpp", "xloctime": "cpp", "xmemory": "cpp", "xstddef": "cpp", "xstring": "cpp", "xtr1common": "cpp", "xtree": "cpp", "xutility": "cpp"}}