﻿#include "basic/mesh/SubMesh.h"

#if defined(_BaseParallelMPI_)
namespace mpi = boost::mpi;
#endif

SubMesh::SubMesh(const int &partID, const int &nLevel)
{
    this->n_pros = partID;
    this->n_level = nLevel;
    this->v_elemID_Global.clear();
    this->v_faceID_Global.clear();
    this->v_nodeID_Global.clear();
    this->v_boundaryID_Global.resize(nLevel);
    if(nLevel > 1) this->v_multiGrid.resize(nLevel - 1);
}

SubMesh::SubMesh(const std::string &fileName, const int &nLevel, const bool &binary)
{
    this->n_pros = GetMPIRank();
    
    std::fstream file;
    if (binary)
    {
        this->st_fileName = fileName + "_p" + ToString(this->n_pros) + ".bMesh";
        file.open(this->st_fileName, std::fstream::in | std::fstream::binary);
    }
    else
    {
        this->st_fileName = fileName + "_p" + ToString(this->n_pros) + ".aMesh";
        file.open(this->st_fileName, std::fstream::in);
    }
    if (!file.is_open()) FatalError("SubMesh::SubMesh: invalid file name");

    this->ReadSubMesh(file, nLevel, binary);

    file.close();
}

void SubMesh::WriteSubMesh(std::fstream &file, const int &nLevel, const bool &binary)
{
    //子网格与全局网格对应关系
    IO::Write(file, this->n_pros, binary);
    IO::Write(file, this->v_nodeID_Global, binary);
    IO::Write(file, this->v_faceID_Global, binary);
	IO::Write(file, this->v_elemID_Global, binary);
	IO::Write(file, this->vv_boundaryFaceIndex_Global, binary);
    IO::Write(file, this->n_level, binary);

    if (this->v_boundaryID_Global.size() < this->n_level)
        this->v_boundaryID_Global.resize(this->n_level);
    IO::Write(file, this->n_level, binary);
    
    //子网格的每一层网格
    for (int level = 0; level < this->n_level; level++)
    {
        if (level > 0)
        {
            MultiGrid* fineGrid = this->GetMultiGrid(level - 1);
            fineGrid->WriteMultiGridGhostElement(file, binary);
        }
        
        IO::Write(file, this->v_boundaryID_Global[level], binary);

        MultiGrid* multiGrid = this->GetMultiGrid(level);
        multiGrid->WriteMultiGrid(file, level, binary);
    }
}

void SubMesh::ReadSubMesh(std::fstream &file, const int &nLevel, const bool &binary)
{
    //子网格与全局网格对应关系
    IO::Read(file, this->n_pros, binary);
    IO::Read(file, this->v_nodeID_Global, binary);
    IO::Read(file, this->v_faceID_Global, binary);
	IO::Read(file, this->v_elemID_Global, binary);
	IO::Read(file, this->vv_boundaryFaceIndex_Global, binary);
    IO::Read(file, this->n_level, binary);

	if (this->n_level < nLevel)
    {
        if (GetMPIRank() == 0) FatalError("SubMesh::SubMesh:粗网格层数不足，请重新前处理！");
    }
    
    this->n_level = nLevel;
    this->v_multiGrid.resize(this->n_level - 1);

    int temp;
    IO::Read(file, temp, binary);
    this->v_boundaryID_Global.resize(temp);

    //子网格的每一层网格
    for (int level = 0; level < this->n_level; level++)
    {
        if (GetMPIRank() == 0) Print("\tRead level " + ToString(level + 1) + " grid ...");
        if (level > 0)
        {
            MultiGrid* fineGrid = this->GetMultiGrid(level - 1);
            fineGrid->ReadMultiGridGhostElement(file, binary);
        }
        
        if (temp > 0) IO::Read(file, this->v_boundaryID_Global[level], binary);

        MultiGrid* coarseGrid = this->GetMultiGrid(level);
        coarseGrid->ReadMultiGrid(file, level, nLevel, binary);
    }
}

void SubMesh::ClearMesh()
{
    this->Mesh::ClearMesh();

    this->v_nodeID_Global.clear();
    this->v_faceID_Global.clear();
    this->v_elemID_Global.clear();
	this->vv_boundaryFaceIndex_Global.clear();
    this->v_boundaryID_Global.clear();
    for (int i = 0; i < v_multiGrid.size(); i++) v_multiGrid[i].ClearMesh();
}

void SubMesh::PreCalculate(const bool &dualFlag, const std::vector<std::vector<int>> &symmetryPatchIDList)
{
    for (int level = 0; level < this->n_level; level++)
    {
        MultiGrid* multiGrid = this->GetMultiGrid(level);
        multiGrid->UpdateElementFaceID();

        //if(level==0) multiGrid->CalculateCenterAndVolume();

        this->CreateGhostElement(level);

        MPIBarrier();

        // 更新真实参与计算的单元信息
        multiGrid->UpdateInDomainInfo();
        
        if (dualFlag)
        {
            multiGrid->UpdateBoundaryElementFlag();

            multiGrid->SetInnerElementIDForBoundaryElement();

            multiGrid->UpdateHalfFaceCrossSymmetryBoundaryFlag(symmetryPatchIDList[level]);
        }
        
        if(level > 0) multiGrid->CalculatefineToCoarseMap();
    }
    
    // 创建单元的面相邻单元列表
    this->vv_neighborID_faceAdjoin.resize(this->n_elemNum);
    for (int elemID = 0; elemID < this->n_elemNum; elemID++)
    {
        std::vector<int> listB;
        
        for (int j = 0; j < this->v_elem[elemID].GetFaceSize(); j++)
        {
            const int &faceID = this->v_elem[elemID].GetFaceID(j);
            int neighID = this->v_face[faceID].GetNeighborID();
            if (neighID == elemID) neighID = this->v_face[faceID].GetOwnerID();
            listB.push_back(neighID);
        }
        this->vv_neighborID_faceAdjoin[elemID] = GetNonRepeatedList(listB);//防止碎面有重复单元才调用此函数
    }
}

const int &SubMesh::GetElementGlobalID(const int &localID)const
{
    if (this->v_elemID_Global.size() > 0) return this->v_elemID_Global[localID];
    else                                  return localID;
}

const int &SubMesh::GetFaceGlobalID(const int &localID)const
{
    if (this->v_faceID_Global.size() > 0) return this->v_faceID_Global[localID];
    else                                  return localID;
}

const int &SubMesh::GetNodeGlobalID(const int &localID)const
{
    if (this->v_nodeID_Global.size() > 0) return this->v_nodeID_Global[localID];
    else                                  return localID;
}

const int &SubMesh::GetBoundaryFaceGlobalIndex(const int &localPatchID, const int &localIndex)const
{
	if (this->vv_boundaryFaceIndex_Global.size() > 0) return this->vv_boundaryFaceIndex_Global[localPatchID][localIndex];
	else                                              return localIndex;
}

const int &SubMesh::GetBoundaryGlobalID(const int & level, const int &localPatchID)const
{
    if(this->v_boundaryID_Global.size() == 0 || this->v_boundaryID_Global[level].size() == 0)
        return localPatchID;
    else
        return this->v_boundaryID_Global[level][localPatchID];
}

MultiGrid* SubMesh::GetMultiGrid(const int &level)
{
    if (level > this->n_level)
    {
        FatalError("SubMesh::GetMultiGrid: level "
            + ToString(level) + " is bigger than total level " + ToString(this->n_level) );
    }

    if (0 == level) return (MultiGrid*)(this);
    else            return &(this->v_multiGrid[level - 1]);
}

void SubMesh::CreateGhostElement(const int &fineLevel)
{
    Mesh *fineMesh = this->GetMultiGrid(fineLevel);

    const int mpiSize = GetMPISize();
    const int mpiRank = GetMPIRank();
    
    // 计算物理边界虚单元的数量
    int n_elemNum_ghostBoundary = 0;
    for (int i = 0; i < (int)fineMesh->GetBoundarySize(); i++)
        n_elemNum_ghostBoundary += fineMesh->GetBoundaryFaceSize(i);
    
    // 计算并行边界虚单元的数量
    int n_elemNum_ghostParallel = 0;
    std::vector<std::vector<GhostElement> > *vv_ghostElement = &fineMesh->vv_ghostElement_parallel;
    for (int i = 0; i < (int)vv_ghostElement->size(); i++)
        n_elemNum_ghostParallel += (*vv_ghostElement)[i].size();
    
    // 计算重叠边界虚单元的数量
    int n_elemNum_ghostOverlap = 0;
    
    // 计算聚合后又分区所产生的虚单元数量
    int n_elemNum_ghostAgglomParallel = 0;
    vv_ghostElement = &fineMesh->vv_ghostElement_multigrid;
    for (int index = 0; index < vv_ghostElement->size(); index++)
    {
        const auto &procPair = (*vv_ghostElement)[index][0].GetProcessorIDPair();
        if (procPair.first == GetMPIRank())
            n_elemNum_ghostAgglomParallel += (*vv_ghostElement)[index].size();
    }

    // 计算单元总大小，并分配空间（防止单元的析构函数执行两次，面列表指针释放两次错误）
    const int n_elemNum_all = fineMesh->v_elem.size() + n_elemNum_ghostParallel
                            + n_elemNum_ghostOverlap + n_elemNum_ghostBoundary
                            + n_elemNum_ghostAgglomParallel;
    fineMesh->v_elem.reserve(n_elemNum_all);
    if (fineLevel==0 && mpiSize > 1)
    {
        this->v_elemID_Global.resize(n_elemNum_all);
        for (int i = fineMesh->n_elemNum; i < fineMesh->n_elemNum_all; i++)
            this->v_elemID_Global[i] = -1;
    }
    
    //0.处理真实单元
    for (int i = 0; i < fineMesh->n_elemNum; i++)
        fineMesh->v_elem[i].et_type = Element::ElemType::real;

    //1.处理物理边界(粗网格虚单元与面对应，允许重复填充)
    fineMesh->CreateBoundaryGhostElement();
    
    //2.处理并行边界
    vv_ghostElement = &fineMesh->vv_ghostElement_parallel;
    const int parallelGhostBoundarySize = vv_ghostElement->size();
    //if (parallelGhostBoundarySize == 0) return;

    if(mpiSize == 1)
    {
        // 用于调试
        for (int i = 0; i < parallelGhostBoundarySize; i++)
        {
            for (int j = 0; j < (*vv_ghostElement)[i].size(); j++)
            {
                //检查网格并行边界faceID
                if ((*vv_ghostElement)[i][j].ID < 0)
                    FatalError( "faceID in vv_ghostElement["
                              + ToString(i) + "][" + ToString(j) + "] isnot wrong!");

                const int &faceID = (*vv_ghostElement)[i][j].ID;
                const int &ownerID = fineMesh->v_face[faceID].n_owner;

                //形成虚拟单元
                Element elemTemp;
                elemTemp.center = 2 * fineMesh->v_face[faceID].center - fineMesh->v_elem[ownerID].center;
                elemTemp.volume = Scalar0;
                elemTemp.et_type = Element::ElemType::ghostParallel;
                fineMesh->v_elem.push_back(elemTemp);
                const int elemTempID = fineMesh->v_elem.size() - 1;
                fineMesh->v_face[faceID].n_neighbor = elemTempID;
                (*vv_ghostElement)[i][j].localIDPair.first = elemTempID;
            }
        }
    }
    else
    {
#if defined(_BaseParallelMPI_)
        std::vector<mpi::request> sendRequests;
        std::vector<mpi::request> recvRequests;

        // 本进程发送真实单元到相邻进程虚单元(真实单元进程到虚单元进程)
        std::vector<std::vector<Vector>> recvRealElementCenter(parallelGhostBoundarySize);
        for (int i = 0; i < parallelGhostBoundarySize; i++)
        {
            const auto &procPair = (*vv_ghostElement)[i][0].procPair;
            recvRealElementCenter[i].resize((*vv_ghostElement)[i].size());
            recvRequests.push_back(MPI::mpiWorld.irecv(procPair.second, procPair.second, recvRealElementCenter[i]));
        }
        for (int i = 0; i < parallelGhostBoundarySize; i++)
        {
            const auto &procPair = (*vv_ghostElement)[i][0].procPair;

            const int count = (*vv_ghostElement)[i].size();
            std::vector<Vector> centerList(count);
            for (int j = 0; j < count; j++)
            {
                const int &faceID = (*vv_ghostElement)[i][j].ID;
                const int &ownerID = fineMesh->v_face[faceID].n_owner;
                centerList[j] = fineMesh->GetElement(ownerID).GetCenter();
            }
            sendRequests.push_back(MPI::mpiWorld.isend(procPair.second, procPair.first, centerList));
        }
        MPIWaitAll(recvRequests);
        MPIWaitAll(sendRequests);

        // 本进程发送真实单元到相邻进程虚单元(真实单元进程到虚单元进程)
        std::vector<std::vector<int>> recvRealElementGlobalID(parallelGhostBoundarySize);
        if (fineLevel==0)
        {
            recvRealElementGlobalID.resize(parallelGhostBoundarySize);
            for (int i = 0; i < parallelGhostBoundarySize; i++)
            {
                const auto &procPair = (*vv_ghostElement)[i][0].procPair;
                recvRealElementGlobalID[i].resize((*vv_ghostElement)[i].size());
                recvRequests.push_back(MPI::mpiWorld.irecv(procPair.second, procPair.second, recvRealElementGlobalID[i]));
            }
            for (int i = 0; i < parallelGhostBoundarySize; i++)
            {
                const auto &procPair = (*vv_ghostElement)[i][0].procPair;

                const int count = (*vv_ghostElement)[i].size();
                std::vector<int> globalIDList(count);
                for (int j = 0; j < count; j++)
                {
                    const int &faceID = (*vv_ghostElement)[i][j].ID;
                    const int &ownerID = fineMesh->v_face[faceID].n_owner;
                    globalIDList[j] = this->v_elemID_Global[ownerID];
                }
                sendRequests.push_back(MPI::mpiWorld.isend(procPair.second, procPair.first, globalIDList));
            }
            MPIWaitAll(recvRequests);
            MPIWaitAll(sendRequests);
        }

        for (int i = 0; i < parallelGhostBoundarySize; i++)
        {
            for (int j = 0; j < (*vv_ghostElement)[i].size(); j++)
            {
                //检查网格并行边界faceID
                if ((*vv_ghostElement)[i][j].ID < 0)
                    FatalError( "faceID in vv_ghostElement["
                              + ToString(i) + "][" + ToString(j) + "] isnot wrong!");

                //形成虚拟单元
                Element elemTemp;
                elemTemp.center = recvRealElementCenter[i][j];
                
                elemTemp.volume = Scalar0;
                elemTemp.et_type = Element::ElemType::ghostParallel;
                fineMesh->v_elem.push_back(elemTemp);
                const int elemTempID = fineMesh->v_elem.size() - 1;
                const int &faceID = (*vv_ghostElement)[i][j].ID;
                fineMesh->v_face[faceID].n_neighbor = elemTempID;
                (*vv_ghostElement)[i][j].localIDPair.first = elemTempID;
                if (fineLevel==0) this->v_elemID_Global[elemTempID] = recvRealElementGlobalID[i][j];
            }
        }
#endif
    }
    fineMesh->n_elemNum_ghostParallel = n_elemNum_ghostParallel;
    fineMesh->n_elemNum_all = fineMesh->v_elem.size();

    //3.处理重叠边界(重叠边界无虚单元)
    fineMesh->n_elemNum_ghostOverlap = n_elemNum_ghostOverlap;
    fineMesh->n_elemNum_all = fineMesh->v_elem.size();
    
    //4.处理聚合后又分区所产生的虚单元
    
    // 最粗网格不包含聚合产生的虚单元，返回
    if(fineLevel >= this->n_level-1) return;
    
    vv_ghostElement = &fineMesh->vv_ghostElement_multigrid;
    const int agglomGhostBoundarySize = vv_ghostElement->size();
    
    // 不能返回，本进程包含其它进程虚单元所对应的真实单元
    //if (agglomGhostBoundarySize == 0) return;

    MultiGrid *coarseMesh = this->GetMultiGrid(fineLevel + 1);
    if(mpiSize == 1)
    {
        // 用于调试
        // 本进程生成虚单元，并填充粗网格与细网格对应关系
        for (int i = 0; i < agglomGhostBoundarySize; i++)
        {
            for (int j = 0; j < (int)(*vv_ghostElement)[i].size(); j++)
            {
                const int &coarseID = (*vv_ghostElement)[i][j].ID;
                std::vector<int> &fineIDList = coarseMesh->v_elemMap[coarseID];
                Scalar totalVolume = 0;
                for (int m = 0; m < fineIDList.size(); m++)
                    totalVolume += fineMesh->v_elem[fineIDList[m]].volume;
                const Scalar &coarseVolume = coarseMesh->v_elem[coarseID].volume;

                Element element;
                element.center = fineMesh->v_elem[fineIDList[0]].center;
                element.volume = std::min(fineMesh->v_elem[fineIDList[0]].volume, coarseVolume - totalVolume);
                element.et_type = Element::ElemType::ghostMultigrid;
                fineMesh->v_elem.push_back(element);

                const int elementID = (int)fineMesh->v_elem.size() - 1;
                (*vv_ghostElement)[i][j].localIDPair.first = elementID;
                coarseMesh->v_elemMap[coarseID].push_back(elementID);
            }
        }
    }
    else
    {
#if defined(_BaseParallelMPI_)
        std::vector<mpi::request> sendRequests;
        std::vector<mpi::request> recvRequests;
        std::vector < std::pair<int, int> > recvTags;

        // step1: 确定虚单元到实单元以及实单元到虚单元的进程关系
        // ghostToRealProcPair大小为当前进程虚单元所对应实单元所在进程的数量
        // ghostToRealProcPair[i]: first为本进程进程号, second为相邻进程进程号
        // realToGhostProcPair大小为当前进程实单元所对应虚单元所在进程的数量
        // realToGhostProcPair[i]: first为相邻进程进程号, second为本进程进程号
        std::vector<std::pair<int, int>> ghostToRealProcPair;
        std::vector<std::pair<int, int>> realToGhostProcPair;
        {
            // step1.1: 明确当前进程的虚单元所对应的真实单元在哪些进程中
            // 信息已知，前处理分区时已存储
            ghostToRealProcPair.resize(agglomGhostBoundarySize);
            for (int i = 0; i < agglomGhostBoundarySize; i++)
                ghostToRealProcPair[i] = (*vv_ghostElement)[i][0].GetProcessorIDPair();

            // step1.2: 明确当前进程中的真实单元是哪些进程的虚单元
            // 信息未知，需要通过MPI收集再从中挑选
            std::vector<std::vector<std::pair<int, int>>> procPairVector(mpiSize);
            all_gather(MPI::mpiWorld, ghostToRealProcPair, procPairVector);
            for (int i = 0; i < procPairVector.size(); i++)
            {
                for (int j = 0; j < procPairVector[i].size(); j++)
                {
                    if(procPairVector[i][j].first != mpiRank && procPairVector[i][j].second == mpiRank)
                        realToGhostProcPair.push_back(procPairVector[i][j]);
                }
            }
            std::vector<std::vector<std::pair<int, int>>>().swap(procPairVector);
        }

        // step2: 明确本进程哪些实单元与其它进程的虚单元对应
        // recvRealIDList为本进程实单元编号，这些实单元与其它进程虚单元对应
        // recvRealIDList与realToGhostProcPair大小相同
        std::vector<std::vector<int>> recvRealIDList(realToGhostProcPair.size());
        {
            // step2.1 先明确recvRealIDList[i]的大小
            std::vector<int> realIDSize(realToGhostProcPair.size());
            for (int i = 0; i < realToGhostProcPair.size(); i++)
            {
                const auto &procPair = realToGhostProcPair[i];
                recvRequests.push_back(MPI::mpiWorld.irecv(procPair.first, procPair.first, realIDSize[i]));
            }
            for (int i = 0; i < ghostToRealProcPair.size(); i++)
            {
                const auto &procPair = ghostToRealProcPair[i];
                int IDSize = (*vv_ghostElement)[i].size();
                sendRequests.push_back(MPI::mpiWorld.isend(procPair.second, procPair.first, IDSize));
            }
            MPIWaitAll(recvRequests);
            MPIWaitAll(sendRequests);

            // step2.2 明确本进程哪些实单元与其它进程的虚单元对应
            // 接受虚单元所对应真实单元编号(虚单元进程到真实单元进程)
            for (int i = 0; i < realToGhostProcPair.size(); i++)
            {
                const auto &procPair = realToGhostProcPair[i];
                recvRealIDList[i].resize(realIDSize[i]);
                recvRequests.push_back(MPI::mpiWorld.irecv(procPair.first, procPair.first, recvRealIDList[i]));
            }
            // 发送虚单元所对应真实单元编号(虚单元进程到真实单元进程)
            for (int i = 0; i < ghostToRealProcPair.size(); i++)
            {
                const auto &procPair = ghostToRealProcPair[i];
                std::vector<int> list((*vv_ghostElement)[i].size());
                for (int j = 0; j < (*vv_ghostElement)[i].size(); j++)
                    list[j] = (*vv_ghostElement)[i][j].GetLocalIDPair().second;
                sendRequests.push_back(MPI::mpiWorld.isend(procPair.second, procPair.first, list));
            }
            MPIWaitAll(recvRequests);
            MPIWaitAll(sendRequests);
        }
        
        // step3: 根据所获得真实单元编号，发送真实单元信息到相邻进程（虚单元所在进程），
        //        并根据接收到的真实单元信息创建本进程虚单元
        {
            // step3.1 传递体心坐标
            std::vector<std::vector<Vector>> realCenterList(ghostToRealProcPair.size());
            for (int i = 0; i < ghostToRealProcPair.size(); i++)
            {
                const auto &procPair = ghostToRealProcPair[i];
                realCenterList[i].resize((*vv_ghostElement)[i].size());
                recvRequests.push_back(MPI::mpiWorld.irecv(procPair.second, procPair.second, realCenterList[i]));
            }
            for (int i = 0; i < realToGhostProcPair.size(); i++)
            {
                const auto &procPair = realToGhostProcPair[i];
                std::vector<Vector> centerList(recvRealIDList[i].size());
                for (int j = 0; j < recvRealIDList[i].size(); j++)
                    centerList[j] = fineMesh->GetElement(recvRealIDList[i][j]).GetCenter();
                sendRequests.push_back(MPI::mpiWorld.isend(procPair.first, procPair.second, centerList));
            }
            MPIWaitAll(recvRequests);
            MPIWaitAll(sendRequests);

            // step3.2 传递体积大小
            std::vector<std::vector<Scalar>> realVolumeList(ghostToRealProcPair.size());
            for (int i = 0; i < ghostToRealProcPair.size(); i++)
            {
                const auto& procPair = ghostToRealProcPair[i];
                realVolumeList[i].resize((*vv_ghostElement)[i].size());
                recvRequests.push_back(MPI::mpiWorld.irecv(procPair.second, procPair.second, realVolumeList[i]));
            }
            for (int i = 0; i < realToGhostProcPair.size(); i++)
            {
                const auto& procPair = realToGhostProcPair[i];
                std::vector<Scalar> list(recvRealIDList[i].size());
                for (int j = 0; j < recvRealIDList[i].size(); j++)
                    list[j] = fineMesh->GetElement(recvRealIDList[i][j]).GetVolume();
                sendRequests.push_back(MPI::mpiWorld.isend(procPair.first, procPair.second, list));
            }
            MPIWaitAll(recvRequests);
            MPIWaitAll(sendRequests);

            // step3.3 根据接收到的信息生成虚单元，并填充粗网格与细网格对应关系
            for (int i = 0; i < agglomGhostBoundarySize; i++)
            {
                for (int j = 0; j < (int)(*vv_ghostElement)[i].size(); j++)
                {
                    Element element;
                    element.et_type = Element::ElemType::ghostMultigrid;
                    element.center = realCenterList[i][j];
                    element.volume = realVolumeList[i][j];
                    fineMesh->v_elem.push_back(element);

                    const int elementID = (int)fineMesh->v_elem.size() - 1;
                    const int &coarseID = (*vv_ghostElement)[i][j].ID;
                    (*vv_ghostElement)[i][j].localIDPair.first = elementID;
                    coarseMesh->v_elemMap[coarseID].push_back(elementID);
                }
            }
        }

        // step4: 传递虚单元隐射关系到真实单元所在进程，并添加到真实单元的vv_ghostElement中
        {
            // step4.1: 发送更新后的虚单元信息到真实单元所在进程（虚单元进程到真实单元进程）
            std::vector<std::vector<GhostElement>> recvGhostElementList(realToGhostProcPair.size());
            for (int i = 0; i < realToGhostProcPair.size(); i++)
            {
                const auto &procPair = realToGhostProcPair[i];
                recvRequests.push_back(MPI::mpiWorld.irecv(procPair.first, procPair.first, recvGhostElementList[i]));
            }
            for (int i = 0; i < ghostToRealProcPair.size(); i++)
            {
                const auto &procPair = ghostToRealProcPair[i];
                sendRequests.push_back(MPI::mpiWorld.isend(procPair.second, procPair.first, (*vv_ghostElement)[i]));
            }
            MPIWaitAll(recvRequests);
            MPIWaitAll(sendRequests);

            // step4.2: 合并本进程已有虚单元信息和相邻进程发送的虚单元信息
            vv_ghostElement->reserve(vv_ghostElement->size() + recvGhostElementList.size());
            for (int i = 0; i < (int)recvGhostElementList.size(); i++)
                vv_ghostElement->push_back(recvGhostElementList[i]);
    
            std::vector<std::vector<GhostElement>>().swap(recvGhostElementList);
        }
#endif
    }
    
    fineMesh->n_elemNum_all = fineMesh->v_elem.size();
}